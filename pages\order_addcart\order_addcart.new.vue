<template>
	<PageLayout iconColor="#000" :isBackgroundColor="false" :isShowBack="false" :navTitle="navTitle"
		:bgMaskStyle="bgMaskStyleComputed">
		<!-- 页面内容 -->
		<view class="page-order-content">
			<view class="page-order-tags">
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img"
							src="/static/images/order-addcart/8d229a503a8d959340f2005e837d5ac83c1fa9d5.svg" />
					</view>
					<view class="page-order-tags-item-text">债券</view>
				</view>
				<view class="page-order-tags-item">
					<view class="page-order-tags-item-icon">
						<image class="page-order-tags-item-icon-img" src="/static/images/order-addcart/discount.svg" />
					</view>
					<view class="page-order-tags-item-text">优惠</view>
				</view>
				<text class="page-order-tags-suffix">
					管理
				</text>
			</view>
			<!-- 所有商品列表 -->
			<view class="page-order-goods-list">
				<!-- 商品项1 -->
				<view class="goods-item" v-for="(item, index) in shopList" :key="item.id">
					<!-- 店名 -->
					<view class="goods-item-header">
						<view class="store-info">
							<uni-data-checkbox selectedColor="#ff4e4b" mode="list" selectedTextColor="#ff4e4b" multiple
								v-model="item.checked" :localdata="[{ value: item.id, text: '' }]"
								@change="(e) => onStoreCheckboxChange(e, index)"></uni-data-checkbox>
							<text class="store-name">{{ item.name }}</text>
						</view>
					</view>
					<!-- 商品列表 -->
					<view class="goods-content-list">
						<view class="goods-content" v-for="goods in item.goodsList" :key="goods.id">
							<view class="goods-checkbox">
								<uni-data-checkbox selectedColor="#ff4e4b" mode="list" selectedTextColor="#ff4e4b"
									multiple v-model="goods.checked" :localdata="[{ value: goods.id, text: '' }]"
									@change="onGoodsCheckboxChange"></uni-data-checkbox>
							</view>

							<view class="goods-image">
								<image class="goods-image-img" :src="goods.image" />
							</view>
							<view class="goods-info">
								<text class="goods-title">{{ goods.name }}</text>
								<view class="goods-spec">
									<text class="goods-spec-text">{{ goods.spec }}</text>
									<image class="goods-spec-arrow"
										src="/static/images/order-addcart/more-right.svg" />
								</view>
								<view class="goods-tags">
									<view class="goods-tag" v-for="tag in goods.tags" :key="tag">
										<text class="goods-tag-text">{{ tag }}</text>
									</view>
								</view>
								<view class="goods-bottom">
									<view class="goods-price">
										<text class="price-label">到手价</text>
										<text class="price-symbol">￥</text>
										<text class="price-value">{{ goods.price }}</text>
									</view>
									<view class="goods-quantity">
										<button class="quantity-btn quantity-minus"
											:class="goods.quantity <= 1 ? 'disabled' : ''"
											@click="changeQuantity(item.id, goods.id, -1)">
											<text class="quantity-btn-text">-</text>
										</button>
										<view class="quantity-input">
											<text class="quantity-text">{{ goods.quantity }}</text>
										</view>
										<button class="quantity-btn quantity-plus"
											@click="changeQuantity(item.id, goods.id, 1)">
											<text class="quantity-btn-text">+</text>
										</button>
									</view>
								</view>
							</view>
						</view>
					</view>

				</view>


			</view>
		</view>

	</PageLayout>
</template>

<script>
import PageLayout from "@/components/PageLayout/index.vue";

export default {
	components: {
		PageLayout,
	},
	data() {
		return {
			navTitle: "购物车",
			shopList: [
				{
					checked: [],
					id: 'store_1',
					name: '泡小燕旗舰店',
					goodsList: [
						{
							id: 'goods_0',
							name: '泡小燕原味燕窝胶原饮90g',
							spec: '原味胶原饮1盒90g',
							price: 164.00,
							quantity: 1,
							checked: [],
							tags: ['债券抵扣¥57.70', '优惠¥3.10'],
							image: 'http://localhost:3845/assets/d0879c0c354f56ddbe9bfc1f80f2624f.png'
						},
						{
							id: 'goods_1',
							name: '泡小燕原味燕窝胶原饮90g',
							spec: '原味胶原饮1盒90g',
							price: 164.00,
							quantity: 1,
							checked: [],
							tags: ['债券抵扣¥57.70', '优惠¥3.10'],
							image: 'http://localhost:3845/assets/d0879c0c354f56ddbe9bfc1f80f2624f.png'
						}
					]
				},
				{
					checked: [],
					id: 'store_2',
					name: '美妆护肤专营店',
					goodsList: [
						{
							id: 'goods_3',
							name: '兰蔻小黑瓶精华液30ml',
							spec: '精华液30ml装',
							price: 899.00,
							quantity: 1,
							checked: [],
							tags: ['新品上市', '限时优惠¥100'],
							image: 'http://localhost:3845/assets/d0879c0c354f56ddbe9bfc1f80f2624f.png'
						},
						{
							id: 'goods_4',
							name: '雅诗兰黛红石榴面霜50ml',
							spec: '面霜50ml装',
							price: 680.00,
							quantity: 2,
							checked: [],
							tags: ['抗氧化', '保湿补水'],
							image: 'http://localhost:3845/assets/d0879c0c354f56ddbe9bfc1f80f2624f.png'
						}
					]
				}
			]
		}
	},
	methods: {
		// 初始化选中状态
		initSelectedState() {
			// 更新店铺选中状态
			this.updateStoreSelection();
		},

		// 店铺复选框状态改变
		onStoreCheckboxChange(e, index) {
			const selectedStoreIds = e.detail.value;
			console.log('店铺选择变化:', index, selectedStoreIds);

			// 遍历所有店铺，根据选中状态更新商品
			this.shopList[index].goodsList.forEach(goods => {
				goods.checked = selectedStoreIds ? [goods.id] : [];
			});
		},

		// 商品复选框状态改变
		onGoodsCheckboxChange(e) {
			console.log('商品选择变化:', e);
			// 更新店铺选中状态
			this.updateStoreSelection();
		},

		// 更新店铺选中状态
		updateStoreSelection() {
			this.shopList.forEach(store => {
				// 检查该店铺下所有商品是否都被选中
				const allGoodsSelected = store.goodsList.every(goods =>
					goods.checked && goods.checked.length > 0
				);

				// 如果所有商品都被选中且商品数量大于0，则店铺也被选中
				if (allGoodsSelected && store.goodsList.length > 0) {
					store.checked = [store.id];
				} else {
					store.checked = [];
				}
			});
		},

		// 数量改变
		changeQuantity(storeId, goodsId, change) {
			const store = this.shopList.find(s => s.id === storeId);
			if (!store) return;

			const goods = store.goodsList.find(g => g.id === goodsId);
			if (!goods) return;

			const newQuantity = goods.quantity + change;

			if (newQuantity >= 1) {
				goods.quantity = newQuantity;
			}
		}
	},
	mounted() {
		// 初始化选中状态
		this.initSelectedState();
	},
	computed: {
		bgMaskStyleComputed() {
			return {
				"--bg-mask-background": `linear-gradient(180deg, #f7d7d6 2.04%, #F5F6FA 10.69%);`
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.page-order-content {
	width: 100%;
	height: 100%;
	background-color: #f5f6fa;
	padding: 32rpx 16rpx;
	display: flex;
	flex-direction: column;
}

.page-order-tags {
	width: 100%;
	height: 60rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.page-order-tags-item {
	width: fit-content;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 8rpx 16rpx;
	background-color: #EBEDF0;
	border-radius: 8px;
	gap: 8rpx;

	&-icon {
		width: 32rpx;
		height: 32rpx;

		&-img {
			width: 100%;
			height: 100%;
		}
	}

	&-text {
		color: #666;
		font-size: 24rpx;
		font-weight: 400;
	}
}

.page-order-tags-suffix {
	color: #666;
	font-size: 28rpx;
	font-weight: 400;
	margin-left: auto;
}

.page-order-goods-list {
	width: 100%;
	margin-top: 32rpx;
	overflow-y: auto;
	flex: 1;
	flex-shrink: 0;
	overflow-x: hidden;
}

.goods-item {
	width: 100%;
	background-color: #ffffff;
	border-radius: 16rpx;
	margin-bottom: 16rpx;
	padding: 16rpx;
}

.goods-item-header {
	width: 100%;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 16rpx;
}

.store-info {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding-left: 16rpx;
}

.store-icon {
	width: 24rpx;
	height: 24rpx;

	&-img {
		width: 100%;
		height: 100%;
	}
}

.store-name {
	color: #131313;
	font-size: 28rpx;
	font-weight: 500;
	font-family: 'PingFang SC', sans-serif;
}

.select-checkbox {
	width: 32rpx;
	height: 32rpx;
	display: flex;
	align-items: center;
	justify-content: center;

	&-img {
		width: 100%;
		height: 100%;
	}
}

.custom-checkbox {
	transform: scale(0.8);
}

.goods-content {
	width: 100%;
	display: flex;
	gap: 24rpx;
	padding: 22rpx 16rpx;
	align-items: center;
}


.goods-image {
	width: 176rpx;
	height: 176rpx;
	border-radius: 16rpx;
	overflow: hidden;
	flex-shrink: 0;

	&-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.goods-info {
	flex: 1;
	display: flex;
	flex-direction: column;
}

.goods-title {
	color: #131313;
	font-size: 28rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
	margin-bottom: 8rpx;
}

.goods-spec {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 16rpx;

	&-text {
		color: #666666;
		font-size: 24rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}

	&-arrow {
		width: 16rpx;
		height: 16rpx;
	}
}

.goods-tags {
	display: flex;
	gap: 16rpx;
	margin-bottom: 16rpx;
}

.goods-tag {
	padding: 4rpx 16rpx;
	background-color: rgba(255, 232, 232, 0.597);
	border-radius: 8rpx;

	&-text {
		color: #ff0000;
		font-size: 20rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}
}

.goods-bottom {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.goods-price {
	display: flex;
	align-items: baseline;
	gap: 8rpx;
}

.price-label {
	color: #ff0000;
	font-size: 24rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
}

.price-symbol {
	color: #ff0000;
	font-size: 28rpx;
	font-weight: 600;
	font-family: 'PingFang SC', sans-serif;
}

.price-value {
	color: #ff0000;
	font-size: 40rpx;
	font-weight: 600;
	font-family: 'PingFang SC', sans-serif;
}

.goods-quantity {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.quantity-btn {
	width: 48rpx;
	height: 48rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: transparent;
	border: none;
	border-radius: 50%;
	padding: 0;
	margin: 0;
	font-size: 32rpx;

	&.disabled {
		opacity: 0.3;
	}

	&-text {
		color: #131313;
		font-size: 32rpx;
		font-weight: 400;
		font-family: 'PingFang SC', sans-serif;
	}

	&-img {
		width: 24rpx;
		height: 24rpx;
	}
}

.quantity-input {
	width: 56rpx;
	height: 48rpx;
	background-color: #f5f5f5;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.quantity-text {
	color: #131313;
	font-size: 24rpx;
	font-weight: 400;
	font-family: 'PingFang SC', sans-serif;
}

.goods-checkbox {
	width: 32rpx;
	height: 32rpx;
}

/deep/.checklist-box {
	padding: 0 !important;
}

/deep/.checkbox__inner {
	border-radius: 50% !important;
}

.goods-content-list {
	display: flex;
	flex-direction: column;
}
</style>